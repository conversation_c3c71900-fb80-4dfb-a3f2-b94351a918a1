# 图像生成配置文件

def get_config():
    """获取图像生成配置"""
    return {
        "default_engine": "pollinations",
        "engines": {
            "pollinations": {
                "enabled": True,
                "name": "Pollinations AI",
                "description": "免费AI图像生成服务",
                "base_url": "https://image.pollinations.ai/prompt",
                "max_concurrent": 5
            },
            "cogview_3_flash": {
                "enabled": True,
                "name": "CogView-3-Flash",
                "description": "智谱AI免费图像生成服务",
                "api_key": "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY",
                "base_url": "https://open.bigmodel.cn/api/paas/v4",
                "max_concurrent": 5,
                "supported_sizes": ["1024x1024", "768x768", "512x512"],
                "default_size": "1024x1024"
            },
            "comfyui_local": {
                "enabled": False,
                "name": "ComfyUI 本地",
                "description": "本地ComfyUI服务",
                "base_url": "http://127.0.0.1:8188",
                "max_concurrent": 3
            }
        }
    }

def get_enabled_engines():
    """获取启用的引擎列表"""
    config = get_config()
    enabled_engines = []
    
    for engine_id, engine_config in config["engines"].items():
        if engine_config.get("enabled", False):
            enabled_engines.append({
                "id": engine_id,
                "name": engine_config["name"],
                "description": engine_config["description"]
            })
    
    return enabled_engines
